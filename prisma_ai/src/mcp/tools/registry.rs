// =================================================================================================
// File: /prisma_ai/src/mcp/tools/registry.rs
// =================================================================================================
// Purpose: Implements MCP tools registry for managing tool registration, discovery, and execution.
// This file provides a centralized registry for both local agent tools and remote MCP tools,
// enabling dynamic tool management and marketplace functionality.
// =================================================================================================
// Internal Dependencies:
// - crate::err: For error handling and result types
// - super::super::client: For MCP client types and tool definitions
// - super::super::server: For MCP server tool handler traits
// - crate::storage: For persistent tool storage and configuration
// =================================================================================================
// External Dependencies:
// - tokio: For async runtime and concurrency
// - serde: For JSON serialization/deserialization
// - tracing: For logging and debugging
// - uuid: For generating unique tool IDs
// - std::collections: For managing tool collections
// =================================================================================================
// Module Interactions:
// - Used by MCP client and server for tool management
// - Integrates with agent manager for exposing agent capabilities
// - Provides tool marketplace functionality for UI integration
// - Manages tool persistence and configuration
// =================================================================================================

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use anyhow::Result;

use crate::err::{PrismaResult, GenericError};
use crate::storage::{DataStore, SurrealDbConnection};
use super::super::client::{McpTool, McpToolResult, McpClientManager};
use super::super::server::ToolHandler;

/// Tool source type
#[derive(Debug, Clone, PartialEq)]
pub enum ToolSource {
    /// Local agent tool
    Local,
    /// Remote MCP server tool
    Remote { server_name: String },
    /// Marketplace tool
    Marketplace { package_name: String, version: String },
}

// Custom serialization for ToolSource to handle SurrealDB compatibility
impl Serialize for ToolSource {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        match self {
            ToolSource::Local => serializer.serialize_str("Local"),
            ToolSource::Remote { server_name } => {
                let value = serde_json::json!({
                    "type": "Remote",
                    "server_name": server_name
                });
                value.serialize(serializer)
            }
            ToolSource::Marketplace { package_name, version } => {
                let value = serde_json::json!({
                    "type": "Marketplace",
                    "package_name": package_name,
                    "version": version
                });
                value.serialize(serializer)
            }
        }
    }
}

impl<'de> Deserialize<'de> for ToolSource {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let value = serde_json::Value::deserialize(deserializer)?;

        match value {
            serde_json::Value::String(s) => {
                match s.as_str() {
                    "Local" => Ok(ToolSource::Local),
                    _ => Err(serde::de::Error::custom(format!("Unknown ToolSource string: {}", s)))
                }
            }
            serde_json::Value::Object(obj) => {
                let type_str = obj.get("type")
                    .and_then(|v| v.as_str())
                    .ok_or_else(|| serde::de::Error::custom("Missing 'type' field in ToolSource object"))?;

                match type_str {
                    "Remote" => {
                        let server_name = obj.get("server_name")
                            .and_then(|v| v.as_str())
                            .ok_or_else(|| serde::de::Error::custom("Missing 'server_name' field in Remote ToolSource"))?
                            .to_string();
                        Ok(ToolSource::Remote { server_name })
                    }
                    "Marketplace" => {
                        let package_name = obj.get("package_name")
                            .and_then(|v| v.as_str())
                            .ok_or_else(|| serde::de::Error::custom("Missing 'package_name' field in Marketplace ToolSource"))?
                            .to_string();
                        let version = obj.get("version")
                            .and_then(|v| v.as_str())
                            .ok_or_else(|| serde::de::Error::custom("Missing 'version' field in Marketplace ToolSource"))?
                            .to_string();
                        Ok(ToolSource::Marketplace { package_name, version })
                    }
                    _ => Err(serde::de::Error::custom(format!("Unknown ToolSource type: {}", type_str)))
                }
            }
            _ => Err(serde::de::Error::custom("ToolSource must be a string or object"))
        }
    }
}

/// Registered tool information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegisteredTool {
    /// Unique tool ID - use custom serialization to handle SurrealDB Thing objects
    #[serde(with = "crate::storage::types::thing_id_string")]
    pub id: String,
    /// Tool name
    pub name: String,
    /// Tool description
    pub description: String,
    /// Input schema
    pub input_schema: serde_json::Value,
    /// Tool source
    pub source: ToolSource,
    /// Whether the tool is enabled
    pub enabled: bool,
    /// Tool configuration
    pub config: Option<serde_json::Value>,
    /// Creation timestamp
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// Last updated timestamp
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

/// Tool execution context
#[derive(Debug, Clone)]
pub struct ToolExecutionContext {
    /// Tool ID
    pub tool_id: String,
    /// Agent ID (if applicable)
    pub agent_id: Option<String>,
    /// User ID (if applicable)
    pub user_id: Option<String>,
    /// Session ID
    pub session_id: Option<String>,
    /// Additional context data
    pub context_data: HashMap<String, serde_json::Value>,
}

/// Tool execution result with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolExecutionResult {
    /// Tool execution result
    pub result: McpToolResult,
    /// Execution metadata
    pub metadata: ToolExecutionMetadata,
}

/// Tool execution metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolExecutionMetadata {
    /// Execution duration in milliseconds
    pub duration_ms: u64,
    /// Tool source
    pub source: ToolSource,
    /// Success status
    pub success: bool,
    /// Error message (if any)
    pub error: Option<String>,
    /// Execution timestamp
    pub executed_at: chrono::DateTime<chrono::Utc>,
}

/// MCP Tools Registry
pub struct McpToolsRegistry {
    /// Registered tools
    tools: Arc<RwLock<HashMap<String, RegisteredTool>>>,
    /// Local tool handlers
    local_handlers: Arc<RwLock<HashMap<String, Arc<dyn ToolHandler>>>>,
    /// MCP client manager for remote tools
    client_manager: Arc<McpClientManager>,
    /// Database connection for persistence
    db: Arc<SurrealDbConnection>,
}

impl McpToolsRegistry {
    /// Create a new tools registry
    pub fn new(client_manager: Arc<McpClientManager>, db: Arc<SurrealDbConnection>) -> Self {
        info!("Creating MCP tools registry");

        Self {
            tools: Arc::new(RwLock::new(HashMap::new())),
            local_handlers: Arc::new(RwLock::new(HashMap::new())),
            client_manager,
            db,
        }
    }

    /// Initialize the registry by loading tools from database
    pub async fn initialize(&self) -> PrismaResult<()> {
        info!("Initializing MCP tools registry");

        // Load tools from database
        match self.load_tools_from_db().await {
            Ok(tools) => {
                let mut tools_map = self.tools.write().await;
                for tool in tools {
                    tools_map.insert(tool.id.clone(), tool);
                }
                info!("Loaded {} tools from database", tools_map.len());
            }
            Err(e) => {
                warn!("Failed to load tools from database: {}", e);
            }
        }

        // Discover tools from connected MCP clients
        self.discover_remote_tools().await?;

        Ok(())
    }

    /// Register a local tool
    pub async fn register_local_tool(
        &self,
        name: &str,
        description: &str,
        input_schema: serde_json::Value,
        handler: Arc<dyn ToolHandler>,
    ) -> PrismaResult<String> {
        let tool_id = Uuid::new_v4().to_string();
        let now = chrono::Utc::now();

        let registered_tool = RegisteredTool {
            id: tool_id.clone(),
            name: name.to_string(),
            description: description.to_string(),
            input_schema,
            source: ToolSource::Local,
            enabled: true,
            config: None,
            created_at: now,
            updated_at: now,
        };

        // Store in registry
        self.tools.write().await.insert(tool_id.clone(), registered_tool.clone());
        self.local_handlers.write().await.insert(tool_id.clone(), handler);

        // Persist to database
        if let Err(e) = self.save_tool_to_db(&registered_tool).await {
            warn!("Failed to save tool to database: {}", e);
        }

        info!("Registered local tool: {} (ID: {})", name, tool_id);
        Ok(tool_id)
    }

    /// Register a remote tool from MCP server
    pub async fn register_remote_tool(
        &self,
        server_name: &str,
        tool: McpTool,
    ) -> PrismaResult<String> {
        // Use the tool name as ID for remote tools to avoid :: in database IDs
        let tool_id = tool.name.clone();
        let now = chrono::Utc::now();

        let registered_tool = RegisteredTool {
            id: tool_id.clone(),
            name: tool.name.clone(),
            description: tool.description.unwrap_or_default(),
            input_schema: tool.input_schema,
            source: ToolSource::Remote {
                server_name: server_name.to_string(),
            },
            enabled: true,
            config: None,
            created_at: now,
            updated_at: now,
        };

        // Store in registry
        self.tools.write().await.insert(tool_id.clone(), registered_tool.clone());

        // Persist to database
        if let Err(e) = self.save_tool_to_db(&registered_tool).await {
            warn!("Failed to save tool to database: {}", e);
        }

        debug!("Registered remote tool: {} from server {} (ID: {})", tool.name, server_name, tool_id);
        Ok(tool_id)
    }

    /// Execute a tool by ID
    pub async fn execute_tool(
        &self,
        tool_id: &str,
        arguments: serde_json::Value,
        _context: ToolExecutionContext,
    ) -> PrismaResult<ToolExecutionResult> {
        let start_time = std::time::Instant::now();
        let execution_time = chrono::Utc::now();

        debug!("Executing tool: {}", tool_id);

        // Get tool information
        let tool = {
            let tools = self.tools.read().await;
            tools.get(tool_id).cloned()
                .ok_or_else(|| GenericError::from(format!("Tool '{}' not found", tool_id)))?
        };

        if !tool.enabled {
            return Err(GenericError::from(format!("Tool '{}' is disabled", tool_id)).into());
        }

        // Execute based on tool source
        let result = match &tool.source {
            ToolSource::Local => {
                self.execute_local_tool(tool_id, arguments).await
            }
            ToolSource::Remote { server_name } => {
                self.execute_remote_tool(server_name, &tool.name, arguments).await
            }
            ToolSource::Marketplace { .. } => {
                Err(GenericError::from("Marketplace tools not yet implemented").into())
            }
        };

        let duration = start_time.elapsed();
        let success = result.is_ok();
        let error_msg = if let Err(ref e) = result {
            Some(e.to_string())
        } else {
            None
        };

        let metadata = ToolExecutionMetadata {
            duration_ms: duration.as_millis() as u64,
            source: tool.source.clone(),
            success,
            error: error_msg,
            executed_at: execution_time,
        };

        match result {
            Ok(tool_result) => {
                debug!("Tool '{}' executed successfully in {}ms", tool_id, metadata.duration_ms);
                Ok(ToolExecutionResult {
                    result: tool_result,
                    metadata,
                })
            }
            Err(e) => {
                error!("Tool '{}' execution failed: {}", tool_id, e);
                Err(e)
            }
        }
    }

    /// Execute a local tool
    async fn execute_local_tool(
        &self,
        tool_id: &str,
        arguments: serde_json::Value,
    ) -> PrismaResult<McpToolResult> {
        let handlers = self.local_handlers.read().await;
        let handler = handlers.get(tool_id)
            .ok_or_else(|| GenericError::from(format!("Local handler for tool '{}' not found", tool_id)))?;

        handler.execute(arguments).await
    }

    /// Execute a remote tool
    async fn execute_remote_tool(
        &self,
        server_name: &str,
        tool_name: &str,
        arguments: serde_json::Value,
    ) -> PrismaResult<McpToolResult> {
        self.client_manager.call_tool(server_name, tool_name, arguments).await
    }

    /// Get all registered tools
    pub async fn get_tools(&self) -> HashMap<String, RegisteredTool> {
        self.tools.read().await.clone()
    }

    /// Get a specific tool by ID
    pub async fn get_tool(&self, tool_id: &str) -> Option<RegisteredTool> {
        self.tools.read().await.get(tool_id).cloned()
    }

    /// Get tools by source type
    pub async fn get_tools_by_source(&self, source_type: &ToolSource) -> Vec<RegisteredTool> {
        let tools = self.tools.read().await;
        tools.values()
            .filter(|tool| std::mem::discriminant(&tool.source) == std::mem::discriminant(source_type))
            .cloned()
            .collect()
    }

    /// Enable or disable a tool
    pub async fn set_tool_enabled(&self, tool_id: &str, enabled: bool) -> PrismaResult<()> {
        let mut tools = self.tools.write().await;
        if let Some(tool) = tools.get_mut(tool_id) {
            tool.enabled = enabled;
            tool.updated_at = chrono::Utc::now();

            // Persist to database
            if let Err(e) = self.save_tool_to_db(tool).await {
                warn!("Failed to save tool to database: {}", e);
            }

            info!("Tool '{}' {} {}", tool_id, if enabled { "enabled" } else { "disabled" }, tool.name);
            Ok(())
        } else {
            Err(GenericError::from(format!("Tool '{}' not found", tool_id)).into())
        }
    }

    /// Remove a tool from the registry
    pub async fn remove_tool(&self, tool_id: &str) -> PrismaResult<()> {
        let mut tools = self.tools.write().await;
        if let Some(tool) = tools.remove(tool_id) {
            // Remove local handler if it exists
            self.local_handlers.write().await.remove(tool_id);

            // Remove from database
            if let Err(e) = self.delete_tool_from_db(tool_id).await {
                warn!("Failed to delete tool from database: {}", e);
            }

            info!("Removed tool: {} (ID: {})", tool.name, tool_id);
            Ok(())
        } else {
            Err(GenericError::from(format!("Tool '{}' not found", tool_id)).into())
        }
    }

    /// Discover tools from connected MCP clients
    async fn discover_remote_tools(&self) -> PrismaResult<()> {
        debug!("Discovering tools from MCP clients");

        let client_names = self.client_manager.list_clients().await;
        for client_name in client_names {
            if let Some(client) = self.client_manager.get_client(&client_name).await {
                let tools = client.get_tools().await;
                for (tool_name, tool) in tools {
                    if let Err(e) = self.register_remote_tool(&client_name, tool).await {
                        warn!("Failed to register remote tool '{}' from client '{}': {}", tool_name, client_name, e);
                    }
                }
            }
        }

        Ok(())
    }

    /// Load tools from database
    async fn load_tools_from_db(&self) -> PrismaResult<Vec<RegisteredTool>> {
        debug!("Loading tools from database");

        let query = "SELECT * FROM mcp_tools";
        println!("Executing query: {}", query);

        // Use the database query method correctly
        match self.db.query::<RegisteredTool>(query, &[]).await {
            Ok(tools) => {
                info!("Loaded {} tools from database", tools.len());
                Ok(tools)
            }
            Err(e) => {
                error!("Failed to load tools from database: {}", e);
                Ok(Vec::new()) // Return empty vec instead of error to allow initialization
            }
        }
    }

    /// Save a tool to database
    async fn save_tool_to_db(&self, tool: &RegisteredTool) -> PrismaResult<()> {
        debug!("Saving tool '{}' to database", tool.id);

        // Use SurrealDB's CREATE with explicit ID to ensure proper record creation
        let _query = format!("CREATE mcp_tools:{} SET updated_at = $updated_at, name = $name, description = $description, input_schema = $input_schema, source = $source, enabled = $enabled, config = $config, created_at = $created_at", tool.id);

        let _params = vec![
            ("updated_at", tool.updated_at.to_rfc3339().as_str()),
            ("name", tool.name.as_str()),
            ("description", tool.description.as_str()),
            ("enabled", if tool.enabled { "true" } else { "false" }),
            ("created_at", tool.created_at.to_rfc3339().as_str()),
        ];

        // Convert input_schema and source to JSON strings for the query
        let input_schema_str = serde_json::to_string(&tool.input_schema)
            .map_err(|e| GenericError::from(format!("Failed to serialize input_schema: {}", e)))?;
        let source_str = serde_json::to_string(&tool.source)
            .map_err(|e| GenericError::from(format!("Failed to serialize source: {}", e)))?;
        let config_str = match &tool.config {
            Some(config) => serde_json::to_string(config)
                .map_err(|e| GenericError::from(format!("Failed to serialize config: {}", e)))?,
            None => "null".to_string(),
        };

        println!("Executing CREATE query: {} SET updated_at = \"{}\", name = \"{}\", description = \"{}\", input_schema = {}, source = {}, enabled = {}, config = {}, created_at = \"{}\"",
                 format!("CREATE mcp_tools:{}", tool.id),
                 tool.updated_at.to_rfc3339(),
                 tool.name,
                 tool.description,
                 input_schema_str,
                 source_str,
                 tool.enabled,
                 config_str,
                 tool.created_at.to_rfc3339());

        // Execute the raw query since we need to handle complex JSON serialization
        let full_query = format!("CREATE mcp_tools:{} SET updated_at = \"{}\", name = \"{}\", description = \"{}\", input_schema = {}, source = {}, enabled = {}, config = {}, created_at = \"{}\"",
                                tool.id,
                                tool.updated_at.to_rfc3339(),
                                tool.name,
                                tool.description,
                                input_schema_str,
                                source_str,
                                tool.enabled,
                                config_str,
                                tool.created_at.to_rfc3339());

        match self.db.query::<RegisteredTool>(&full_query, &[]).await {
            Ok(_) => {
                debug!("Successfully saved tool to database: {}", tool.id);
                Ok(())
            }
            Err(e) => {
                error!("Failed to save tool to database: {}", e);
                Err(e)
            }
        }
    }

    /// Delete a tool from database
    async fn delete_tool_from_db(&self, tool_id: &str) -> PrismaResult<()> {
        debug!("Deleting tool '{}' from database", tool_id);

        match self.db.delete("mcp_tools", tool_id).await {
            Ok(_) => {
                debug!("Deleted tool from database: {}", tool_id);
                Ok(())
            }
            Err(e) => {
                error!("Failed to delete tool from database: {}", e);
                Err(e)
            }
        }
    }

    /// Get tool usage statistics
    pub async fn get_tool_stats(&self, tool_id: &str) -> PrismaResult<ToolStats> {
        debug!("Getting stats for tool: {}", tool_id);

        // For now, return default stats since we don't have complex query support
        // In a real implementation, this would query the mcp_tool_executions table
        warn!("Tool statistics not yet implemented - returning default stats");
        Ok(ToolStats::default())
    }

    /// Record tool execution for statistics
    pub async fn record_execution(&self, tool_id: &str, metadata: &ToolExecutionMetadata) -> PrismaResult<()> {
        debug!("Recording execution for tool: {}", tool_id);

        // For now, just log the execution since we don't have complex query support
        // In a real implementation, this would create a record in mcp_tool_executions table
        info!("Tool '{}' executed: success={}, duration={}ms",
              tool_id, metadata.success, metadata.duration_ms);

        Ok(())
    }
}

/// Tool usage statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolStats {
    /// Total number of executions
    pub total_executions: u64,
    /// Number of successful executions
    pub successful_executions: u64,
    /// Average execution duration in milliseconds
    pub avg_duration_ms: f64,
    /// Last execution timestamp
    pub last_executed: Option<chrono::DateTime<chrono::Utc>>,
}

impl Default for ToolStats {
    fn default() -> Self {
        Self {
            total_executions: 0,
            successful_executions: 0,
            avg_duration_ms: 0.0,
            last_executed: None,
        }
    }
}
