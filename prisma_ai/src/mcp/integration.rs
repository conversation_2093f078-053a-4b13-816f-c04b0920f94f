// =================================================================================================
// File: /prisma_ai/src/mcp/integration.rs
// =================================================================================================
// Purpose: Implements integration between MCP functionality and the existing agent manager system.
// This file bridges MCP tools and resources with agent capabilities, enabling agents to use
// external MCP tools and expose their capabilities through MCP servers.
// =================================================================================================
// Internal Dependencies:
// - crate::err: For error handling and result types
// - super::client: For MCP client management and tool execution
// - super::server: For MCP server functionality and tool handlers
// - super::tools: For tools registry and handler management
// - crate::prisma::prisma_engine::agent_manager: For agent management integration
// - crate::storage: For persistent storage of MCP configurations
// =================================================================================================
// External Dependencies:
// - tokio: For async runtime and concurrency
// - serde: For JSON serialization/deserialization
// - tracing: For logging and debugging
// - std::collections: For managing configurations and mappings
// - uuid: For generating unique identifiers
// =================================================================================================
// Module Interactions:
// - Integrates with agent manager for exposing agent capabilities
// - Uses MCP client manager for accessing external tools
// - Manages MCP server instances for exposing agent tools
// - Provides configuration management for MCP integrations
// =================================================================================================

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, Mutex};
use tracing::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use anyhow::{Context, Result};

use crate::err::{PrismaResult, GenericError, DomainError};
use crate::storage::{DataStore, SurrealDbConnection};
use super::client::{McpClientManager, McpClientConfig, McpToolResult};
use super::server::{McpServer, McpServerConfig, ToolHandler, ResourceHandler};
use super::tools::registry::{McpToolsRegistry, RegisteredTool, ToolExecutionContext, ToolExecutionResult};
use super::tools::handlers::{FileSystemHandler, HttpHandler};
use super::transport::{TransportConfig, TransportFactory};

/// MCP integration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpIntegrationConfig {
    /// Integration ID
    pub id: String,
    /// Integration name
    pub name: String,
    /// Integration description
    pub description: Option<String>,
    /// MCP client configurations
    pub clients: Vec<McpClientConfig>,
    /// MCP server configurations
    pub servers: Vec<McpServerConfig>,
    /// Tool marketplace settings
    pub marketplace: McpMarketplaceConfig,
    /// Whether the integration is enabled
    pub enabled: bool,
}

/// MCP marketplace configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpMarketplaceConfig {
    /// Whether marketplace is enabled
    pub enabled: bool,
    /// Marketplace URL
    pub url: Option<String>,
    /// Auto-install popular tools
    pub auto_install_popular: bool,
    /// Maximum number of tools to auto-install
    pub max_auto_install: u32,
    /// Allowed tool categories
    pub allowed_categories: Vec<String>,
}

impl Default for McpMarketplaceConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            url: None,
            auto_install_popular: false,
            max_auto_install: 10,
            allowed_categories: vec![
                "filesystem".to_string(),
                "web".to_string(),
                "database".to_string(),
                "ai".to_string(),
            ],
        }
    }
}

/// Agent capability mapping to MCP tools
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentCapabilityMapping {
    /// Agent capability name
    pub capability: String,
    /// Mapped MCP tool ID
    pub tool_id: String,
    /// Parameter mapping configuration
    pub parameter_mapping: HashMap<String, String>,
    /// Whether the mapping is enabled
    pub enabled: bool,
}

/// MCP integration manager
pub struct McpIntegrationManager {
    /// Integration configuration
    config: Arc<RwLock<McpIntegrationConfig>>,
    /// MCP client manager
    client_manager: Arc<McpClientManager>,
    /// MCP servers
    servers: Arc<RwLock<HashMap<String, Arc<McpServer>>>>,
    /// Tools registry
    tools_registry: Arc<McpToolsRegistry>,
    /// Agent capability mappings
    capability_mappings: Arc<RwLock<HashMap<String, AgentCapabilityMapping>>>,
    /// Database connection
    db: Arc<SurrealDbConnection>,
    /// Running status
    running: Arc<Mutex<bool>>,
}

impl McpIntegrationManager {
    /// Create a new MCP integration manager
    pub fn new(
        config: McpIntegrationConfig,
        db: Arc<SurrealDbConnection>,
    ) -> PrismaResult<Self> {
        info!("Creating MCP integration manager: {}", config.name);

        let client_manager = Arc::new(McpClientManager::new());
        let tools_registry = Arc::new(McpToolsRegistry::new(
            Arc::clone(&client_manager),
            Arc::clone(&db),
        ));

        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            client_manager,
            servers: Arc::new(RwLock::new(HashMap::new())),
            tools_registry,
            capability_mappings: Arc::new(RwLock::new(HashMap::new())),
            db,
            running: Arc::new(Mutex::new(false)),
        })
    }

    /// Initialize the MCP integration
    pub async fn initialize(&self) -> PrismaResult<()> {
        info!("Initializing MCP integration");

        // Initialize tools registry
        self.tools_registry.initialize().await?;

        // Load capability mappings from database
        self.load_capability_mappings().await?;

        // Initialize MCP clients
        self.initialize_clients().await?;

        // Initialize MCP servers
        self.initialize_servers().await?;

        // Register default tool handlers
        self.register_default_handlers().await?;

        *self.running.lock().await = true;

        info!("MCP integration initialized successfully");
        Ok(())
    }

    /// Initialize MCP clients from configuration
    async fn initialize_clients(&self) -> PrismaResult<()> {
        let config = self.config.read().await;

        for client_config in &config.clients {
            debug!("Initializing MCP client: {}", client_config.name);

            match self.client_manager.add_client(client_config.clone()).await {
                Ok(_) => {
                    info!("Successfully initialized MCP client: {}", client_config.name);
                }
                Err(e) => {
                    error!("Failed to initialize MCP client '{}': {}", client_config.name, e);
                    // Continue with other clients instead of failing completely
                }
            }
        }

        Ok(())
    }

    /// Initialize MCP servers from configuration
    async fn initialize_servers(&self) -> PrismaResult<()> {
        let config = self.config.read().await;

        for server_config in &config.servers {
            debug!("Initializing MCP server: {}", server_config.name);

            let server = Arc::new(McpServer::new(server_config.clone()));

            // Register tool handlers for this server
            for tool in &server_config.tools {
                if let Some(handler) = self.create_tool_handler(&tool.handler).await {
                    server.register_tool_handler(&tool.name, handler).await;
                }
            }

            // Start the server
            match server.start().await {
                Ok(_) => {
                    self.servers.write().await.insert(server_config.name.clone(), server);
                    info!("Successfully initialized MCP server: {}", server_config.name);
                }
                Err(e) => {
                    error!("Failed to initialize MCP server '{}': {}", server_config.name, e);
                }
            }
        }

        Ok(())
    }

    /// Register default tool handlers
    async fn register_default_handlers(&self) -> PrismaResult<()> {
        debug!("Registering default tool handlers");

        // Register file system handler
        let fs_handler = Arc::new(FileSystemHandler::new("./", false)); // Read-only by default
        self.tools_registry.register_local_tool(
            "filesystem",
            "File system operations (read-only)",
            serde_json::json!({
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["read", "list", "exists"]
                    },
                    "path": {
                        "type": "string",
                        "description": "File or directory path"
                    }
                },
                "required": ["operation", "path"]
            }),
            fs_handler,
        ).await?;

        // Register HTTP handler
        let http_handler = Arc::new(HttpHandler::new(None)); // No domain restrictions by default
        self.tools_registry.register_local_tool(
            "http",
            "HTTP operations",
            serde_json::json!({
                "type": "object",
                "properties": {
                    "method": {
                        "type": "string",
                        "enum": ["GET", "POST", "PUT", "DELETE", "HEAD"],
                        "default": "GET"
                    },
                    "url": {
                        "type": "string",
                        "description": "URL to request"
                    },
                    "headers": {
                        "type": "object",
                        "description": "HTTP headers"
                    },
                    "body": {
                        "description": "Request body"
                    }
                },
                "required": ["url"]
            }),
            http_handler,
        ).await?;

        info!("Default tool handlers registered");
        Ok(())
    }

    /// Create a tool handler by name
    async fn create_tool_handler(&self, handler_name: &str) -> Option<Arc<dyn ToolHandler>> {
        match handler_name {
            "filesystem" => Some(Arc::new(FileSystemHandler::new("./", false))),
            "filesystem_rw" => Some(Arc::new(FileSystemHandler::new("./", true))),
            "http" => Some(Arc::new(HttpHandler::new(None))),
            _ => {
                warn!("Unknown tool handler: {}", handler_name);
                None
            }
        }
    }

    /// Execute a tool through the integration
    pub async fn execute_tool(
        &self,
        tool_id: &str,
        arguments: serde_json::Value,
        agent_id: Option<String>,
        user_id: Option<String>,
    ) -> PrismaResult<ToolExecutionResult> {
        debug!("Executing tool '{}' through MCP integration", tool_id);

        let context = ToolExecutionContext {
            tool_id: tool_id.to_string(),
            agent_id,
            user_id,
            session_id: Some(Uuid::new_v4().to_string()),
            context_data: HashMap::new(),
        };

        let result = self.tools_registry.execute_tool(tool_id, arguments, context).await?;

        // Record execution for statistics
        if let Err(e) = self.tools_registry.record_execution(tool_id, &result.metadata).await {
            warn!("Failed to record tool execution: {}", e);
        }

        Ok(result)
    }

    /// Get all available tools
    pub async fn get_available_tools(&self) -> HashMap<String, RegisteredTool> {
        self.tools_registry.get_tools().await
    }

    /// Add a new MCP client configuration
    pub async fn add_client(&self, client_config: McpClientConfig) -> PrismaResult<()> {
        info!("Adding MCP client: {}", client_config.name);

        // Add to client manager
        self.client_manager.add_client(client_config.clone()).await?;

        // Update configuration
        let mut config = self.config.write().await;
        config.clients.push(client_config);

        // Save configuration to database
        self.save_config_to_db(&config).await?;

        // Discover new tools from the client
        self.tools_registry.initialize().await?;

        Ok(())
    }

    /// Remove an MCP client
    pub async fn remove_client(&self, client_name: &str) -> PrismaResult<()> {
        info!("Removing MCP client: {}", client_name);

        // Remove from client manager
        self.client_manager.remove_client(client_name).await?;

        // Update configuration
        let mut config = self.config.write().await;
        config.clients.retain(|c| c.name != client_name);

        // Save configuration to database
        self.save_config_to_db(&config).await?;

        // Remove tools from this client
        let tools = self.tools_registry.get_tools().await;
        for (tool_id, tool) in tools {
            if let super::tools::registry::ToolSource::Remote { server_name } = &tool.source {
                if server_name == client_name {
                    if let Err(e) = self.tools_registry.remove_tool(&tool_id).await {
                        warn!("Failed to remove tool '{}': {}", tool_id, e);
                    }
                }
            }
        }

        Ok(())
    }

    /// Map an agent capability to an MCP tool
    pub async fn map_agent_capability(
        &self,
        capability: &str,
        tool_id: &str,
        parameter_mapping: HashMap<String, String>,
    ) -> PrismaResult<()> {
        info!("Mapping agent capability '{}' to tool '{}'", capability, tool_id);

        // Verify the tool exists
        if self.tools_registry.get_tool(tool_id).await.is_none() {
            return Err(GenericError::from(format!("Tool '{}' not found", tool_id)).into());
        }

        let mapping = AgentCapabilityMapping {
            capability: capability.to_string(),
            tool_id: tool_id.to_string(),
            parameter_mapping,
            enabled: true,
        };

        // Store mapping
        self.capability_mappings.write().await.insert(capability.to_string(), mapping.clone());

        // Save to database
        self.save_capability_mapping(&mapping).await?;

        Ok(())
    }

    /// Execute an agent capability through MCP tools
    pub async fn execute_agent_capability(
        &self,
        capability: &str,
        parameters: serde_json::Value,
        agent_id: Option<String>,
        user_id: Option<String>,
    ) -> PrismaResult<ToolExecutionResult> {
        debug!("Executing agent capability '{}' through MCP", capability);

        // Get capability mapping
        let mapping = {
            let mappings = self.capability_mappings.read().await;
            mappings.get(capability).cloned()
                .ok_or_else(|| GenericError::from(format!("No MCP mapping found for capability '{}'", capability)))?
        };

        if !mapping.enabled {
            return Err(GenericError::from(format!("Capability mapping for '{}' is disabled", capability)).into());
        }

        // Transform parameters using mapping
        let mut transformed_params = parameters;
        if let Some(params_obj) = transformed_params.as_object_mut() {
            for (from_key, to_key) in &mapping.parameter_mapping {
                if let Some(value) = params_obj.remove(from_key) {
                    params_obj.insert(to_key.clone(), value);
                }
            }
        }

        // Execute the mapped tool
        self.execute_tool(&mapping.tool_id, transformed_params, agent_id, user_id).await
    }

    /// Get marketplace tools (placeholder for future implementation)
    pub async fn get_marketplace_tools(&self) -> PrismaResult<Vec<MarketplaceTool>> {
        debug!("Getting marketplace tools");

        let config = self.config.read().await;
        if !config.marketplace.enabled {
            return Ok(Vec::new());
        }

        // This would integrate with an actual marketplace API
        // For now, return some example tools
        Ok(vec![
            MarketplaceTool {
                id: "playwright-mcp".to_string(),
                name: "Playwright MCP".to_string(),
                description: "Browser automation tools using Playwright".to_string(),
                category: "web".to_string(),
                version: "1.0.0".to_string(),
                install_command: "npx @microsoft/playwright-mcp".to_string(),
                docker_image: Some("mcr.microsoft.com/playwright:latest".to_string()),
            },
            MarketplaceTool {
                id: "filesystem-mcp".to_string(),
                name: "Filesystem MCP".to_string(),
                description: "File system operations for MCP".to_string(),
                category: "filesystem".to_string(),
                version: "1.0.0".to_string(),
                install_command: "npx @modelcontextprotocol/server-filesystem".to_string(),
                docker_image: None,
            },
        ])
    }

    /// Install a marketplace tool
    pub async fn install_marketplace_tool(
        &self,
        tool_id: &str,
        use_docker: bool,
    ) -> PrismaResult<()> {
        info!("Installing marketplace tool: {} (docker: {})", tool_id, use_docker);

        let marketplace_tools = self.get_marketplace_tools().await?;
        let tool = marketplace_tools.iter()
            .find(|t| t.id == tool_id)
            .ok_or_else(|| GenericError::from(format!("Marketplace tool '{}' not found", tool_id)))?;

        // Create transport configuration
        let transport_config = if use_docker {
            if let Some(ref docker_image) = tool.docker_image {
                TransportFactory::create_docker_config(docker_image, vec![])
            } else {
                return Err(GenericError::from(format!("Tool '{}' does not support Docker", tool_id)).into());
            }
        } else {
            // Parse npx command
            let parts: Vec<&str> = tool.install_command.split_whitespace().collect();
            if parts.len() < 2 || parts[0] != "npx" {
                return Err(GenericError::from(format!("Invalid install command: {}", tool.install_command)).into());
            }

            let package = parts[1];
            let args = parts[2..].iter().map(|s| s.to_string()).collect();
            TransportFactory::create_npx_config(package, args)
        };

        // Create client configuration
        let client_config = McpClientConfig {
            name: tool.id.clone(),
            transport: transport_config,
            timeout: Some(30),
            auto_reconnect: true,
            max_reconnect_attempts: Some(3),
        };

        // Add the client
        self.add_client(client_config).await?;

        info!("Successfully installed marketplace tool: {}", tool_id);
        Ok(())
    }

    /// Shutdown the MCP integration
    pub async fn shutdown(&self) -> PrismaResult<()> {
        info!("Shutting down MCP integration");

        *self.running.lock().await = false;

        // Shutdown MCP clients
        self.client_manager.shutdown().await?;

        // Shutdown MCP servers
        let servers = self.servers.read().await;
        for (name, server) in servers.iter() {
            if let Err(e) = server.stop().await {
                warn!("Failed to stop MCP server '{}': {}", name, e);
            }
        }

        info!("MCP integration shutdown complete");
        Ok(())
    }

    /// Load capability mappings from database
    async fn load_capability_mappings(&self) -> PrismaResult<()> {
        debug!("Loading capability mappings from database");

        let query = "SELECT * FROM mcp_capability_mappings";
        match self.db.query::<AgentCapabilityMapping>(query, &[]).await {
            Ok(mappings) => {
                let mut mappings_map = self.capability_mappings.write().await;
                for mapping in mappings {
                    mappings_map.insert(mapping.capability.clone(), mapping);
                }

                info!("Loaded {} capability mappings from database", mappings_map.len());
            }
            Err(e) => {
                warn!("Failed to load capability mappings from database: {}", e);
            }
        }

        Ok(())
    }

    /// Save configuration to database
    async fn save_config_to_db(&self, config: &McpIntegrationConfig) -> PrismaResult<()> {
        debug!("Saving MCP integration config to database");
        debug!("Config ID: '{}', Name: '{}'", config.id, config.name);

        // Check if ID is empty or invalid
        if config.id.is_empty() {
            error!("Config ID is empty, cannot save to database");
            return Err(GenericError::from(DomainError::ValidationError("Config ID cannot be empty".to_string())));
        }

        // Try to update first, if it doesn't exist, create it
        debug!("Attempting to update config with ID: '{}'", config.id);
        match self.db.update("mcp_integration_configs", &config.id, config).await {
            Ok(_) => {
                debug!("Updated existing config in database: {}", config.id);
                Ok(())
            }
            Err(update_error) => {
                debug!("Update failed: {}, attempting to create new config", update_error);
                // Config doesn't exist, create it
                match self.db.create("mcp_integration_configs", config).await {
                    Ok(_) => {
                        debug!("Created new config in database: {}", config.id);
                        Ok(())
                    }
                    Err(create_error) => {
                        error!("Failed to save config to database - Update error: {}, Create error: {}", update_error, create_error);
                        Err(create_error)
                    }
                }
            }
        }
    }

    /// Save capability mapping to database
    async fn save_capability_mapping(&self, mapping: &AgentCapabilityMapping) -> PrismaResult<()> {
        debug!("Saving capability mapping to database: {}", mapping.capability);

        // Try to update first, if it doesn't exist, create it
        match self.db.update("mcp_capability_mappings", &mapping.capability, mapping).await {
            Ok(_) => {
                debug!("Updated existing mapping in database: {}", mapping.capability);
                Ok(())
            }
            Err(_) => {
                // Mapping doesn't exist, create it
                match self.db.create("mcp_capability_mappings", mapping).await {
                    Ok(_) => {
                        debug!("Created new mapping in database: {}", mapping.capability);
                        Ok(())
                    }
                    Err(e) => {
                        error!("Failed to save mapping to database: {}", e);
                        Err(e)
                    }
                }
            }
        }
    }

    /// Get integration status
    pub async fn get_status(&self) -> McpIntegrationStatus {
        let running = *self.running.lock().await;
        let config = self.config.read().await;
        let client_names = self.client_manager.list_clients().await;
        let server_names: Vec<String> = self.servers.read().await.keys().cloned().collect();
        let tools_count = self.tools_registry.get_tools().await.len();
        let mappings_count = self.capability_mappings.read().await.len();

        McpIntegrationStatus {
            running,
            integration_name: config.name.clone(),
            clients_count: client_names.len(),
            servers_count: server_names.len(),
            tools_count,
            capability_mappings_count: mappings_count,
            client_names,
            server_names,
        }
    }
}

/// Marketplace tool definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketplaceTool {
    /// Tool ID
    pub id: String,
    /// Tool name
    pub name: String,
    /// Tool description
    pub description: String,
    /// Tool category
    pub category: String,
    /// Tool version
    pub version: String,
    /// NPX install command
    pub install_command: String,
    /// Docker image (optional)
    pub docker_image: Option<String>,
}

/// MCP integration status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpIntegrationStatus {
    /// Whether the integration is running
    pub running: bool,
    /// Integration name
    pub integration_name: String,
    /// Number of connected clients
    pub clients_count: usize,
    /// Number of running servers
    pub servers_count: usize,
    /// Number of available tools
    pub tools_count: usize,
    /// Number of capability mappings
    pub capability_mappings_count: usize,
    /// List of client names
    pub client_names: Vec<String>,
    /// List of server names
    pub server_names: Vec<String>,
}

/// Create default MCP integration configuration
pub fn create_default_config() -> McpIntegrationConfig {
    McpIntegrationConfig {
        id: Uuid::new_v4().to_string(),
        name: "Default MCP Integration".to_string(),
        description: Some("Default MCP integration with common tools".to_string()),
        clients: vec![
            // Playwright MCP client (npx)
            McpClientConfig {
                name: "playwright-mcp".to_string(),
                transport: TransportFactory::create_npx_config(
                    "@microsoft/playwright-mcp",
                    vec![]
                ),
                timeout: Some(30),
                auto_reconnect: true,
                max_reconnect_attempts: Some(3),
            },
            // Filesystem MCP client (npx)
            McpClientConfig {
                name: "filesystem-mcp".to_string(),
                transport: TransportFactory::create_npx_config(
                    "@modelcontextprotocol/server-filesystem",
                    vec!["--allowed-directory".to_string(), "./".to_string()]
                ),
                timeout: Some(30),
                auto_reconnect: true,
                max_reconnect_attempts: Some(3),
            },
        ],
        servers: vec![],
        marketplace: McpMarketplaceConfig::default(),
        enabled: true,
    }
}
