{"name": "@automatalabs/mcp-server-playwright", "version": "1.2.1", "description": "MCP server for browser automation using Playwright", "license": "MIT", "author": "Automata Labs (https://automatalabs.io)", "homepage": "https://automatalabs.io", "repository": {"type": "git", "url": "git+https://github.com/Automata-Labs-team/MCP-Server-Playwright.git"}, "bugs": "https://github.com/Automata-Labs-team/MCP-Server-Playwright/issues", "type": "module", "bin": {"mcp-server-playwright": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "0.5.0", "playwright": "^1.48.0", "yargs": "^17.7.2"}, "devDependencies": {"@types/node": "^22.10.2", "@types/yargs": "^17.0.33", "shx": "^0.3.4", "typescript": "^5.6.2"}}